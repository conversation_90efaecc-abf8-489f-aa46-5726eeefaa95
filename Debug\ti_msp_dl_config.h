/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for MotorFront */
#define MotorFront_INST                                                    TIMG0
#define MotorFront_INST_IRQHandler                              TIMG0_IRQHandler
#define MotorFront_INST_INT_IRQN                                (TIMG0_INT_IRQn)
#define MotorFront_INST_CLK_FREQ                                         1000000
/* GPIO defines for channel 0 */
#define GPIO_MotorFront_C0_PORT                                            GPIOA
#define GPIO_MotorFront_C0_PIN                                    DL_GPIO_PIN_12
#define GPIO_MotorFront_C0_IOMUX                                 (IOMUX_PINCM34)
#define GPIO_MotorFront_C0_IOMUX_FUNC                IOMUX_PINCM34_PF_TIMG0_CCP0
#define GPIO_MotorFront_C0_IDX                               DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MotorFront_C1_PORT                                            GPIOA
#define GPIO_MotorFront_C1_PIN                                    DL_GPIO_PIN_13
#define GPIO_MotorFront_C1_IOMUX                                 (IOMUX_PINCM35)
#define GPIO_MotorFront_C1_IOMUX_FUNC                IOMUX_PINCM35_PF_TIMG0_CCP1
#define GPIO_MotorFront_C1_IDX                               DL_TIMER_CC_1_INDEX

/* Defines for MotorBack */
#define MotorBack_INST                                                     TIMG8
#define MotorBack_INST_IRQHandler                               TIMG8_IRQHandler
#define MotorBack_INST_INT_IRQN                                 (TIMG8_INT_IRQn)
#define MotorBack_INST_CLK_FREQ                                          1000000
/* GPIO defines for channel 0 */
#define GPIO_MotorBack_C0_PORT                                             GPIOA
#define GPIO_MotorBack_C0_PIN                                      DL_GPIO_PIN_1
#define GPIO_MotorBack_C0_IOMUX                                   (IOMUX_PINCM2)
#define GPIO_MotorBack_C0_IOMUX_FUNC                  IOMUX_PINCM2_PF_TIMG8_CCP0
#define GPIO_MotorBack_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MotorBack_C1_PORT                                             GPIOA
#define GPIO_MotorBack_C1_PIN                                      DL_GPIO_PIN_0
#define GPIO_MotorBack_C1_IOMUX                                   (IOMUX_PINCM1)
#define GPIO_MotorBack_C1_IOMUX_FUNC                  IOMUX_PINCM1_PF_TIMG8_CCP1
#define GPIO_MotorBack_C1_IDX                                DL_TIMER_CC_1_INDEX




/* Defines for I2C_MPU6050 */
#define I2C_MPU6050_INST                                                    I2C0
#define I2C_MPU6050_INST_IRQHandler                              I2C0_IRQHandler
#define I2C_MPU6050_INST_INT_IRQN                                  I2C0_INT_IRQn
#define I2C_MPU6050_BUS_SPEED_HZ                                          400000
#define GPIO_I2C_MPU6050_SDA_PORT                                          GPIOA
#define GPIO_I2C_MPU6050_SDA_PIN                                  DL_GPIO_PIN_28
#define GPIO_I2C_MPU6050_IOMUX_SDA                                (IOMUX_PINCM3)
#define GPIO_I2C_MPU6050_IOMUX_SDA_FUNC                 IOMUX_PINCM3_PF_I2C0_SDA
#define GPIO_I2C_MPU6050_SCL_PORT                                          GPIOA
#define GPIO_I2C_MPU6050_SCL_PIN                                  DL_GPIO_PIN_31
#define GPIO_I2C_MPU6050_IOMUX_SCL                                (IOMUX_PINCM6)
#define GPIO_I2C_MPU6050_IOMUX_SCL_FUNC                 IOMUX_PINCM6_PF_I2C0_SCL

/* Defines for I2C_OLED */
#define I2C_OLED_INST                                                       I2C1
#define I2C_OLED_INST_IRQHandler                                 I2C1_IRQHandler
#define I2C_OLED_INST_INT_IRQN                                     I2C1_INT_IRQn
#define I2C_OLED_BUS_SPEED_HZ                                             400000
#define GPIO_I2C_OLED_SDA_PORT                                             GPIOB
#define GPIO_I2C_OLED_SDA_PIN                                      DL_GPIO_PIN_3
#define GPIO_I2C_OLED_IOMUX_SDA                                  (IOMUX_PINCM16)
#define GPIO_I2C_OLED_IOMUX_SDA_FUNC                   IOMUX_PINCM16_PF_I2C1_SDA
#define GPIO_I2C_OLED_SCL_PORT                                             GPIOB
#define GPIO_I2C_OLED_SCL_PIN                                      DL_GPIO_PIN_2
#define GPIO_I2C_OLED_IOMUX_SCL                                  (IOMUX_PINCM15)
#define GPIO_I2C_OLED_IOMUX_SCL_FUNC                   IOMUX_PINCM15_PF_I2C1_SCL


/* Defines for UART0 */
#define UART0_INST                                                         UART0
#define UART0_INST_IRQHandler                                   UART0_IRQHandler
#define UART0_INST_INT_IRQN                                       UART0_INT_IRQn
#define GPIO_UART0_RX_PORT                                                 GPIOA
#define GPIO_UART0_TX_PORT                                                 GPIOA
#define GPIO_UART0_RX_PIN                                         DL_GPIO_PIN_11
#define GPIO_UART0_TX_PIN                                         DL_GPIO_PIN_10
#define GPIO_UART0_IOMUX_RX                                      (IOMUX_PINCM22)
#define GPIO_UART0_IOMUX_TX                                      (IOMUX_PINCM21)
#define GPIO_UART0_IOMUX_RX_FUNC                       IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART0_IOMUX_TX_FUNC                       IOMUX_PINCM21_PF_UART0_TX
#define UART0_BAUD_RATE                                                 (115200)
#define UART0_IBRD_40_MHZ_115200_BAUD                                       (21)
#define UART0_FBRD_40_MHZ_115200_BAUD                                       (45)





/* Defines for DMA_CH_RX */
#define DMA_CH_RX_CHAN_ID                                                    (1)
#define UART0_INST_DMA_TRIGGER_0                             (DMA_UART0_RX_TRIG)

/* Defines for DMA_CH_TX */
#define DMA_CH_TX_CHAN_ID                                                    (0)
#define UART0_INST_DMA_TRIGGER_1                             (DMA_UART0_TX_TRIG)



/* Port definition for Pin Group BUZZ */
#define BUZZ_PORT                                                        (GPIOA)

/* Defines for Periph: GPIOA.3 with pinCMx 8 on package pin 43 */
#define BUZZ_Periph_PIN                                          (DL_GPIO_PIN_3)
#define BUZZ_Periph_IOMUX                                         (IOMUX_PINCM8)
/* Port definition for Pin Group GPIO_MPU6050 */
#define GPIO_MPU6050_PORT                                                (GPIOA)

/* Defines for PIN_INT: GPIOA.30 with pinCMx 5 on package pin 37 */
// groups represented: ["SPD_READER_A","GPIO_MPU6050"]
// pins affected: ["FONT_LEFT_A","FONT_RIGHT_A","BACK_LEFT_A","BACK_RIGHT_A","PIN_INT"]
#define GPIO_MULTIPLE_GPIOA_INT_IRQN                            (GPIOA_INT_IRQn)
#define GPIO_MULTIPLE_GPIOA_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define GPIO_MPU6050_PIN_INT_IIDX                           (DL_GPIO_IIDX_DIO30)
#define GPIO_MPU6050_PIN_INT_PIN                                (DL_GPIO_PIN_30)
#define GPIO_MPU6050_PIN_INT_IOMUX                                (IOMUX_PINCM5)
/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOB)

/* Defines for Board: GPIOB.14 with pinCMx 31 on package pin 2 */
#define LED_Board_PIN                                           (DL_GPIO_PIN_14)
#define LED_Board_IOMUX                                          (IOMUX_PINCM31)
/* Defines for RED: GPIOB.23 with pinCMx 51 on package pin 22 */
#define LED_RED_PIN                                             (DL_GPIO_PIN_23)
#define LED_RED_IOMUX                                            (IOMUX_PINCM51)
/* Defines for BLUE: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_BLUE_PIN                                            (DL_GPIO_PIN_22)
#define LED_BLUE_IOMUX                                           (IOMUX_PINCM50)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for S2: GPIOB.21 with pinCMx 49 on package pin 20 */
#define KEY_S2_PIN                                              (DL_GPIO_PIN_21)
#define KEY_S2_IOMUX                                             (IOMUX_PINCM49)
/* Defines for K1: GPIOB.19 with pinCMx 45 on package pin 16 */
#define KEY_K1_PIN                                              (DL_GPIO_PIN_19)
#define KEY_K1_IOMUX                                             (IOMUX_PINCM45)
/* Defines for K2: GPIOB.20 with pinCMx 48 on package pin 19 */
#define KEY_K2_PIN                                              (DL_GPIO_PIN_20)
#define KEY_K2_IOMUX                                             (IOMUX_PINCM48)
/* Port definition for Pin Group SPD_READER_A */
#define SPD_READER_A_PORT                                                (GPIOA)

/* Defines for FONT_LEFT_A: GPIOA.27 with pinCMx 60 on package pin 31 */
#define SPD_READER_A_FONT_LEFT_A_IIDX                       (DL_GPIO_IIDX_DIO27)
#define SPD_READER_A_FONT_LEFT_A_PIN                            (DL_GPIO_PIN_27)
#define SPD_READER_A_FONT_LEFT_A_IOMUX                           (IOMUX_PINCM60)
/* Defines for FONT_RIGHT_A: GPIOA.26 with pinCMx 59 on package pin 30 */
#define SPD_READER_A_FONT_RIGHT_A_IIDX                      (DL_GPIO_IIDX_DIO26)
#define SPD_READER_A_FONT_RIGHT_A_PIN                           (DL_GPIO_PIN_26)
#define SPD_READER_A_FONT_RIGHT_A_IOMUX                          (IOMUX_PINCM59)
/* Defines for BACK_LEFT_A: GPIOA.25 with pinCMx 55 on package pin 26 */
#define SPD_READER_A_BACK_LEFT_A_IIDX                       (DL_GPIO_IIDX_DIO25)
#define SPD_READER_A_BACK_LEFT_A_PIN                            (DL_GPIO_PIN_25)
#define SPD_READER_A_BACK_LEFT_A_IOMUX                           (IOMUX_PINCM55)
/* Defines for BACK_RIGHT_A: GPIOA.24 with pinCMx 54 on package pin 25 */
#define SPD_READER_A_BACK_RIGHT_A_IIDX                      (DL_GPIO_IIDX_DIO24)
#define SPD_READER_A_BACK_RIGHT_A_PIN                           (DL_GPIO_PIN_24)
#define SPD_READER_A_BACK_RIGHT_A_IOMUX                          (IOMUX_PINCM54)
/* Port definition for Pin Group SPD_READER_B */
#define SPD_READER_B_PORT                                                (GPIOB)

/* Defines for FONT_LEFT_B: GPIOB.10 with pinCMx 27 on package pin 62 */
#define SPD_READER_B_FONT_LEFT_B_PIN                            (DL_GPIO_PIN_10)
#define SPD_READER_B_FONT_LEFT_B_IOMUX                           (IOMUX_PINCM27)
/* Defines for FONT_RIGHT_B: GPIOB.11 with pinCMx 28 on package pin 63 */
#define SPD_READER_B_FONT_RIGHT_B_PIN                           (DL_GPIO_PIN_11)
#define SPD_READER_B_FONT_RIGHT_B_IOMUX                          (IOMUX_PINCM28)
/* Defines for BACK_LEFT_B: GPIOB.12 with pinCMx 29 on package pin 64 */
#define SPD_READER_B_BACK_LEFT_B_PIN                            (DL_GPIO_PIN_12)
#define SPD_READER_B_BACK_LEFT_B_IOMUX                           (IOMUX_PINCM29)
/* Defines for BACK_RIGHT_B: GPIOB.13 with pinCMx 30 on package pin 1 */
#define SPD_READER_B_BACK_RIGHT_B_PIN                           (DL_GPIO_PIN_13)
#define SPD_READER_B_BACK_RIGHT_B_IOMUX                          (IOMUX_PINCM30)
/* Port definition for Pin Group DIRC_CTRL */
#define DIRC_CTRL_PORT                                                   (GPIOB)

/* Defines for FONT_LEFT: GPIOB.6 with pinCMx 23 on package pin 58 */
#define DIRC_CTRL_FONT_LEFT_PIN                                  (DL_GPIO_PIN_6)
#define DIRC_CTRL_FONT_LEFT_IOMUX                                (IOMUX_PINCM23)
/* Defines for FONT_RIGHT: GPIOB.7 with pinCMx 24 on package pin 59 */
#define DIRC_CTRL_FONT_RIGHT_PIN                                 (DL_GPIO_PIN_7)
#define DIRC_CTRL_FONT_RIGHT_IOMUX                               (IOMUX_PINCM24)
/* Defines for BACK_LEFT: GPIOB.8 with pinCMx 25 on package pin 60 */
#define DIRC_CTRL_BACK_LEFT_PIN                                  (DL_GPIO_PIN_8)
#define DIRC_CTRL_BACK_LEFT_IOMUX                                (IOMUX_PINCM25)
/* Defines for BACK_RIGHT: GPIOB.9 with pinCMx 26 on package pin 61 */
#define DIRC_CTRL_BACK_RIGHT_PIN                                 (DL_GPIO_PIN_9)
#define DIRC_CTRL_BACK_RIGHT_IOMUX                               (IOMUX_PINCM26)
/* Port definition for Pin Group Tracker */
#define Tracker_PORT                                                     (GPIOB)

/* Defines for _1: GPIOB.0 with pinCMx 12 on package pin 47 */
#define Tracker__1_PIN                                           (DL_GPIO_PIN_0)
#define Tracker__1_IOMUX                                         (IOMUX_PINCM12)
/* Defines for _2: GPIOB.1 with pinCMx 13 on package pin 48 */
#define Tracker__2_PIN                                           (DL_GPIO_PIN_1)
#define Tracker__2_IOMUX                                         (IOMUX_PINCM13)
/* Defines for _3: GPIOB.4 with pinCMx 17 on package pin 52 */
#define Tracker__3_PIN                                           (DL_GPIO_PIN_4)
#define Tracker__3_IOMUX                                         (IOMUX_PINCM17)
/* Defines for _4: GPIOB.5 with pinCMx 18 on package pin 53 */
#define Tracker__4_PIN                                           (DL_GPIO_PIN_5)
#define Tracker__4_IOMUX                                         (IOMUX_PINCM18)
/* Defines for _5: GPIOB.15 with pinCMx 32 on package pin 3 */
#define Tracker__5_PIN                                          (DL_GPIO_PIN_15)
#define Tracker__5_IOMUX                                         (IOMUX_PINCM32)
/* Defines for _6: GPIOB.16 with pinCMx 33 on package pin 4 */
#define Tracker__6_PIN                                          (DL_GPIO_PIN_16)
#define Tracker__6_IOMUX                                         (IOMUX_PINCM33)
/* Defines for _7: GPIOB.17 with pinCMx 43 on package pin 14 */
#define Tracker__7_PIN                                          (DL_GPIO_PIN_17)
#define Tracker__7_IOMUX                                         (IOMUX_PINCM43)
/* Defines for _8: GPIOB.18 with pinCMx 44 on package pin 15 */
#define Tracker__8_PIN                                          (DL_GPIO_PIN_18)
#define Tracker__8_IOMUX                                         (IOMUX_PINCM44)





/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_MotorFront_init(void);
void SYSCFG_DL_MotorBack_init(void);
void SYSCFG_DL_I2C_MPU6050_init(void);
void SYSCFG_DL_I2C_OLED_init(void);
void SYSCFG_DL_UART0_init(void);
void SYSCFG_DL_DMA_init(void);

void SYSCFG_DL_SYSTICK_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
