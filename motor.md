# TB6612电机驱动配置指南

## 概述
本文档指导您使用CCS的SysConfig工具将现有的A4950电机驱动配置修改为TB6612电机驱动配置。

## A4950 vs TB6612 差异分析

### A4950 (当前配置)
- **控制方式**: PWM + 方向控制
- **引脚需求**: 每个电机需要2个引脚 (1个PWM + 1个方向控制)
- **当前配置**:
  - 前轮PWM: PA12 (TIMG0_CCP0), PA13 (TIMG0_CCP1)
  - 后轮PWM: PA1 (TIMG8_CCP0), PA0 (TIMG8_CCP1)
  - 方向控制: PB6, PB7, PB8, PB9

### TB6612 (目标配置)
- **控制方式**: IN1/IN2方向控制 + PWM调速
- **引脚需求**: 每个电机需要3个引脚 (IN1 + IN2 + PWM)，4路共用1个STBY
- **优势**: 更精确的速度控制，支持制动模式，4路集成驱动

## SysConfig配置步骤

### 第一步：修改现有方向控制GPIO配置

1. 打开 `empty.syscfg` 文件
2. 找到 `GPIO5` (DIRC_CTRL) 配置段
3. 修改为TB6612的IN1/IN2控制：
   ```javascript
   GPIO5.$name = "TB6612_CTRL";
   GPIO5.associatedPins[0].$name = "FRONT_LEFT_IN1";   // PB6
   GPIO5.associatedPins[1].$name = "FRONT_LEFT_IN2";   // PB7
   GPIO5.associatedPins[2].$name = "FRONT_RIGHT_IN1";  // PB8
   GPIO5.associatedPins[3].$name = "FRONT_RIGHT_IN2";  // PB9
   ```

### 第二步：保持现有PWM配置用于调速

#### 2.1 前轮PWM配置 (MotorFront)
保持现有的TIMG0配置用于前轮调速：

```javascript
PWM1.$name = "MotorFront";
PWM1.pwmMode = "EDGE_ALIGN_UP";
PWM1.timerStartTimer = true;
PWM1.timerCount = 100;
PWM1.clockPrescale = 40;
PWM1.peripheral.$assign = "TIMG0";

// 左前轮PWM调速
PWM1.PWM_CHANNEL_0.$name = "FRONT_LEFT_PWM";
PWM1.PWM_CHANNEL_0.dutyCycle = 0;
// 右前轮PWM调速
PWM1.PWM_CHANNEL_1.$name = "FRONT_RIGHT_PWM";
PWM1.PWM_CHANNEL_1.dutyCycle = 0;
```

#### 2.2 后轮PWM配置 (MotorBack)
保持现有的TIMG8配置用于后轮调速：

```javascript
PWM2.$name = "MotorBack";
PWM2.pwmMode = "EDGE_ALIGN_UP";
PWM2.timerStartTimer = true;
PWM2.timerCount = 100;
PWM2.clockPrescale = 40;
PWM2.peripheral.$assign = "TIMG8";

// 左后轮PWM调速
PWM2.PWM_CHANNEL_0.$name = "BACK_LEFT_PWM";
PWM2.PWM_CHANNEL_0.dutyCycle = 0;
// 右后轮PWM调速
PWM2.PWM_CHANNEL_1.$name = "BACK_RIGHT_PWM";
PWM2.PWM_CHANNEL_1.dutyCycle = 0;
```

### 第三步：添加后轮IN1/IN2控制GPIO

需要为后轮添加IN1/IN2方向控制引脚：

```javascript
// 添加GPIO6用于后轮IN1/IN2控制
// 注意：需要重新分配Tracker的引脚或使用其他可用引脚
const GPIO_BACK_CTRL = GPIO.addInstance();
GPIO_BACK_CTRL.$name = "TB6612_BACK_CTRL";
GPIO_BACK_CTRL.port = "PORTA";
GPIO_BACK_CTRL.associatedPins.create(4);
GPIO_BACK_CTRL.associatedPins[0].$name = "BACK_LEFT_IN1";
GPIO_BACK_CTRL.associatedPins[0].assignedPin = "14";  // 可用引脚
GPIO_BACK_CTRL.associatedPins[1].$name = "BACK_LEFT_IN2";
GPIO_BACK_CTRL.associatedPins[1].assignedPin = "15";  // 可用引脚
GPIO_BACK_CTRL.associatedPins[2].$name = "BACK_RIGHT_IN1";
GPIO_BACK_CTRL.associatedPins[2].assignedPin = "16";  // 可用引脚
GPIO_BACK_CTRL.associatedPins[3].$name = "BACK_RIGHT_IN2";
GPIO_BACK_CTRL.associatedPins[3].assignedPin = "17";  // 可用引脚
```

### 第四步：配置STBY控制引脚

添加单个STBY控制引脚（4路共用）：

```javascript
// 添加单个STBY控制引脚
const GPIO_STBY = GPIO.addInstance();
GPIO_STBY.$name = "TB6612_STBY";
GPIO_STBY.port = "PORTA";
GPIO_STBY.associatedPins.create(1);
GPIO_STBY.associatedPins[0].$name = "STBY";
GPIO_STBY.associatedPins[0].assignedPin = "18";  // 选择可用引脚
GPIO_STBY.associatedPins[0].initialValue = "SET";  // 默认使能
```

### 第五步：引脚分配建议

#### TB6612引脚分配方案：
```
左前轮电机:
- IN1: PB6 (GPIO方向控制)
- IN2: PB7 (GPIO方向控制)
- PWM: PA12 (TIMG0_CCP0) - 调速

右前轮电机:
- IN1: PB8 (GPIO方向控制)
- IN2: PB9 (GPIO方向控制)
- PWM: PA13 (TIMG0_CCP1) - 调速

左后轮电机:
- IN1: PA14 (GPIO方向控制)
- IN2: PA15 (GPIO方向控制)
- PWM: PA1 (TIMG8_CCP0) - 调速

右后轮电机:
- IN1: PA16 (GPIO方向控制)
- IN2: PA17 (GPIO方向控制)
- PWM: PA0 (TIMG8_CCP1) - 调速

公共控制:
- STBY: PA18 (4路共用使能控制)
```

## 配置验证

### 检查项目：
1. ✅ 确认PWM通道用于调速控制
2. ✅ 确认IN1/IN2 GPIO用于方向控制
3. ✅ 确认STBY引脚配置正确（4路共用）
4. ✅ 确认引脚分配无冲突
5. ✅ 确认时钟配置一致

### 生成配置后检查：
1. 查看生成的 `ti_msp_dl_config.h` 文件
2. 确认所有宏定义正确生成：
   - TB6612_CTRL_PORT 和相关PIN定义
   - TB6612_STBY_PORT 和PIN定义
   - MotorFront/MotorBack PWM定义
3. 验证引脚映射表

## 注意事项

1. **引脚冲突**: 确保新分配的引脚不与Tracker等现有功能冲突
2. **控制逻辑**: TB6612使用IN1/IN2组合控制方向，PWM控制速度
3. **电源管理**: TB6612需要独立的电源供应，STBY控制整体使能
4. **PCB布局**: 确认硬件PCB支持新的引脚配置

## TB6612控制逻辑说明

### 电机控制方式：
```
IN1  IN2  PWM   电机状态
0    0    X     停止（制动）
0    1    PWM   正转（调速）
1    0    PWM   反转（调速）
1    1    X     停止（制动）
STBY=0          所有电机停止
```

### 控制优势：
1. **精确方向控制**: IN1/IN2组合提供4种状态
2. **独立调速**: PWM信号独立控制速度
3. **制动功能**: 支持电机制动模式
4. **统一使能**: STBY控制所有电机的总开关

## 下一步

配置完成后，需要修改以下代码文件：
- `BSP/Inc/Motor.h` - 更新电机结构体定义，增加IN1/IN2控制
- `BSP/Src/Motor.c` - 实现TB6612控制逻辑（IN1/IN2+PWM）
- 更新README.md中的引脚分配表

完成SysConfig配置后，请运行代码生成，然后可以开始修改驱动代码。
