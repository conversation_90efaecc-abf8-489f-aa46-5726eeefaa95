# TB6612电机驱动配置指南

## 概述
本文档指导您使用CCS的SysConfig工具将现有的A4950电机驱动配置修改为TB6612电机驱动配置。

## A4950 vs TB6612 差异分析

### A4950 (当前配置)
- **控制方式**: PWM + 方向控制
- **引脚需求**: 每个电机需要2个引脚 (1个PWM + 1个方向控制)
- **当前配置**:
  - 前轮PWM: PA12 (TIMG0_CCP0), PA13 (TIMG0_CCP1)
  - 后轮PWM: PA1 (TIMG8_CCP0), PA0 (TIMG8_CCP1)
  - 方向控制: PB6, PB7, PB8, PB9

### TB6612 (目标配置)
- **控制方式**: 双PWM控制 (IN1/IN2)
- **引脚需求**: 每个电机需要3个引脚 (2个PWM + 1个STBY)
- **优势**: 更精确的速度控制，支持制动模式

## SysConfig配置步骤

### 第一步：删除现有方向控制GPIO配置

1. 打开 `empty.syscfg` 文件
2. 找到 `GPIO5` (DIRC_CTRL) 配置段
3. 删除以下配置项：
   ```javascript
   GPIO5.$name = "DIRC_CTRL";
   GPIO5.associatedPins[0].$name = "FONT_LEFT";    // PB6
   GPIO5.associatedPins[1].$name = "FONT_RIGHT";   // PB7
   GPIO5.associatedPins[2].$name = "BACK_LEFT";    // PB8
   GPIO5.associatedPins[3].$name = "BACK_RIGHT";   // PB9
   ```

### 第二步：重新配置PWM模块

#### 2.1 修改前轮PWM配置 (MotorFront)
保持现有的TIMG0配置，但需要增加更多PWM通道：

```javascript
PWM1.$name = "MotorFront";
PWM1.pwmMode = "EDGE_ALIGN_UP";
PWM1.timerStartTimer = true;
PWM1.timerCount = 100;
PWM1.clockPrescale = 40;
PWM1.peripheral.$assign = "TIMG0";

// 左前轮 - IN1/IN2
PWM1.PWM_CHANNEL_0.$name = "FRONT_LEFT_IN1";
PWM1.PWM_CHANNEL_0.dutyCycle = 0;
PWM1.PWM_CHANNEL_1.$name = "FRONT_LEFT_IN2";
PWM1.PWM_CHANNEL_1.dutyCycle = 0;

// 右前轮 - IN1/IN2 (需要添加更多通道或使用其他定时器)
```

#### 2.2 修改后轮PWM配置 (MotorBack)
```javascript
PWM2.$name = "MotorBack";
PWM2.pwmMode = "EDGE_ALIGN_UP";
PWM2.timerStartTimer = true;
PWM2.timerCount = 100;
PWM2.clockPrescale = 40;
PWM2.peripheral.$assign = "TIMG8";

// 左后轮 - IN1/IN2
PWM2.PWM_CHANNEL_0.$name = "BACK_LEFT_IN1";
PWM2.PWM_CHANNEL_0.dutyCycle = 0;
PWM2.PWM_CHANNEL_1.$name = "BACK_LEFT_IN2";
PWM2.PWM_CHANNEL_1.dutyCycle = 0;
```

### 第三步：添加额外PWM定时器

由于TB6612需要更多PWM通道，需要添加额外的PWM定时器：

```javascript
// 添加PWM3用于右前轮
const PWM3 = PWM.addInstance();
PWM3.$name = "MotorFrontRight";
PWM3.pwmMode = "EDGE_ALIGN_UP";
PWM3.timerStartTimer = true;
PWM3.timerCount = 100;
PWM3.clockPrescale = 40;
PWM3.peripheral.$assign = "TIMG1";
PWM3.PWM_CHANNEL_0.$name = "FRONT_RIGHT_IN1";
PWM3.PWM_CHANNEL_1.$name = "FRONT_RIGHT_IN2";

// 添加PWM4用于右后轮
const PWM4 = PWM.addInstance();
PWM4.$name = "MotorBackRight";
PWM4.pwmMode = "EDGE_ALIGN_UP";
PWM4.timerStartTimer = true;
PWM4.timerCount = 100;
PWM4.clockPrescale = 40;
PWM4.peripheral.$assign = "TIMG7";
PWM4.PWM_CHANNEL_0.$name = "BACK_RIGHT_IN1";
PWM4.PWM_CHANNEL_1.$name = "BACK_RIGHT_IN2";
```

### 第四步：配置STBY控制引脚

添加新的GPIO配置用于TB6612的STBY控制：

```javascript
// 修改GPIO5为STBY控制
GPIO5.$name = "TB6612_STBY";
GPIO5.port = "PORTB";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name = "STBY_FRONT";
GPIO5.associatedPins[0].assignedPin = "6";
GPIO5.associatedPins[0].initialValue = "SET";
GPIO5.associatedPins[1].$name = "STBY_BACK";
GPIO5.associatedPins[1].assignedPin = "7";
GPIO5.associatedPins[1].initialValue = "SET";
```

### 第五步：引脚分配建议

#### TB6612引脚分配方案：
```
左前轮电机 (Motor A1):
- AIN1: PA12 (TIMG0_CCP0) - 原PWM1通道0
- AIN2: PA13 (TIMG0_CCP1) - 原PWM1通道1
- STBY: PB6

右前轮电机 (Motor A2):
- AIN1: PA14 (TIMG1_CCP0) - 新增PWM3通道0
- AIN2: PA15 (TIMG1_CCP1) - 新增PWM3通道1
- STBY: PB6 (共用)

左后轮电机 (Motor B1):
- BIN1: PA1 (TIMG8_CCP0) - 原PWM2通道0
- BIN2: PA0 (TIMG8_CCP1) - 原PWM2通道1
- STBY: PB7

右后轮电机 (Motor B2):
- BIN1: PA16 (TIMG7_CCP0) - 新增PWM4通道0
- BIN2: PA17 (TIMG7_CCP1) - 新增PWM4通道1
- STBY: PB7 (共用)
```

## 配置验证

### 检查项目：
1. ✅ 确认所有PWM通道已正确配置
2. ✅ 确认引脚分配无冲突
3. ✅ 确认STBY引脚配置正确
4. ✅ 确认时钟配置一致

### 生成配置后检查：
1. 查看生成的 `ti_msp_dl_config.h` 文件
2. 确认所有宏定义正确生成
3. 验证引脚映射表

## 注意事项

1. **引脚冲突**: 确保新分配的引脚不与现有功能冲突
2. **定时器资源**: 检查MSPM0G3507可用的定时器资源
3. **电源管理**: TB6612需要独立的电源供应
4. **PCB布局**: 确认硬件PCB支持新的引脚配置

## 下一步

配置完成后，需要修改以下代码文件：
- `BSP/Inc/Motor.h` - 更新电机结构体定义
- `BSP/Src/Motor.c` - 实现TB6612控制逻辑
- 更新README.md中的引脚分配表

完成SysConfig配置后，请运行代码生成，然后可以开始修改驱动代码。
