******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 10:59:51 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000072f9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000096c8  00016938  R  X
  SRAM                  20200000   00008000  000006b1  0000794f  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000096c8   000096c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007eb0   00007eb0    r-x .text
  00007f70    00007f70    000016d0   000016d0    r-- .rodata
  00009640    00009640    00000088   00000088    r-- .cinit
20200000    20200000    000004b2   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    0000018e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007eb0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000284     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001370    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  000015e8    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001820    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a4c    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001c78    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001e98    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  0000208c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002268    000001b0     Task.o (.text.Task_Start)
                  00002418    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000025b8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000274a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000274c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000028d4    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a4c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002bbc    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002d24    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002e68    0000013c     Tracker.o (.text.Tracker_Read)
                  00002fa4    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000030e0    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003214    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003348    00000130     OLED.o (.text.OLED_ShowChar)
                  00003478    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000035a8    00000128     inv_mpu.o (.text.mpu_init)
                  000036d0    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  000037f4    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003918    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a38    00000110     OLED.o (.text.OLED_Init)
                  00003b48    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003c54    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003d5c    00000104     Task_App.o (.text.Task_Motor_PID)
                  00003e60    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003f60    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  0000404c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004130    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004214    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000042f0    000000dc     Task_App.o (.text.Task_OLED)
                  000043cc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000044a4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000457c    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004650    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004720    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  000047e4    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000048a8    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  0000496c    000000bc     Motor.o (.text.Motor_Start)
                  00004a28    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004ae4    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004b9c    000000b4     Task.o (.text.Task_Add)
                  00004c50    000000b0     Task_App.o (.text.Task_Init)
                  00004d00    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004dac    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004e58    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00004efc    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004f9e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004fa0    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005040    0000009c     Motor.o (.text.Motor_SetDuty)
                  000050dc    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005178    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005210    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000052a8    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000533e    00000002     --HOLE-- [fill = 0]
                  00005340    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000053cc    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005458    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000054dc    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005560    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000055e2    00000002     --HOLE-- [fill = 0]
                  000055e4    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00005664    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  000056e4    00000080     Task_App.o (.text.Task_Serial)
                  00005764    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000057e0    00000074     Motor.o (.text.Motor_SetDirc)
                  00005854    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000058c8    00000008     Interrupt.o (.text.SysTick_Handler)
                  000058d0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005944    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000059b8    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005a28    0000006e     OLED.o (.text.OLED_ShowString)
                  00005a96    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005b00    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005b68    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005bce    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005c34    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005c98    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005cfc    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005d60    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005dc2    00000002     --HOLE-- [fill = 0]
                  00005dc4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005e26    00000002     --HOLE-- [fill = 0]
                  00005e28    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005e88    00000060     Key_Led.o (.text.Key_Read)
                  00005ee8    00000060     Task_App.o (.text.Task_IdleFunction)
                  00005f48    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005fa8    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006008    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006066    00000002     --HOLE-- [fill = 0]
                  00006068    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000060c4    0000005c     Task_App.o (.text.Task_Tracker)
                  00006120    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000617c    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000061d4    00000058     Serial.o (.text.Serial_Init)
                  0000622c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006284    00000058            : _printfi.c.obj (.text._pconv_f)
                  000062dc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006332    00000002     --HOLE-- [fill = 0]
                  00006334    00000054     Interrupt.o (.text.Interrupt_Init)
                  00006388    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  000063dc    00000054     OLED.o (.text.mspm0_i2c_enable)
                  00006430    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006482    00000002     --HOLE-- [fill = 0]
                  00006484    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000064d4    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006524    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006574    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000065c0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000660c    0000004c     OLED.o (.text.OLED_Printf)
                  00006658    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000066a2    00000002     --HOLE-- [fill = 0]
                  000066a4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000066ec    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006734    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  0000677c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000067c0    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006804    00000044     Task_App.o (.text.Task_Key)
                  00006848    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  0000688c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000068d0    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00006914    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006958    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000699a    00000002     --HOLE-- [fill = 0]
                  0000699c    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000069dc    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006a1c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006a5c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006a9c    0000003e     Task.o (.text.Task_CMP)
                  00006ada    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006b18    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006b54    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006b90    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006bcc    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006c08    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006c44    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006c80    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006cbc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006cf8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006d32    00000002     --HOLE-- [fill = 0]
                  00006d34    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006d6e    00000002     --HOLE-- [fill = 0]
                  00006d70    00000038     Task_App.o (.text.Task_LED)
                  00006da8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006de0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e14    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e48    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e7c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006eb0    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006ee2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006f14    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006f44    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006f74    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00006fa4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006fd4    00000030            : vsnprintf.c.obj (.text._outs)
                  00007004    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007034    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007064    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007090    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000070bc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000070e8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007114    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000713e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007166    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000718e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000071b6    00000002     --HOLE-- [fill = 0]
                  000071b8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000071e0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007208    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007230    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007258    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007280    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000072a8    00000028     SysTick.o (.text.SysTick_Increasment)
                  000072d0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000072f8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007320    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007346    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000736c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007392    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000073b8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000073dc    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007400    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007422    00000002     --HOLE-- [fill = 0]
                  00007424    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007444    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007464    00000020     SysTick.o (.text.Delay)
                  00007484    00000020     main.o (.text.main)
                  000074a4    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  000074c4    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000074e2    00000002     --HOLE-- [fill = 0]
                  000074e4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007502    00000002     --HOLE-- [fill = 0]
                  00007504    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007520    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  0000753c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007558    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007574    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007590    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000075ac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000075c8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000075e4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007600    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000761c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007638    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007654    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007670    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000768c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000076a8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000076c4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000076e0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000076fc    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007718    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007730    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007748    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007760    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007778    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007790    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000077a8    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000077c0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000077d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000077f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007808    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007820    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007838    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007850    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007868    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007880    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007898    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000078b0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000078c8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000078e0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000078f8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007910    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007928    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007940    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007958    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007970    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007988    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000079a0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000079b8    00000018     OLED.o (.text.DL_I2C_reset)
                  000079d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000079e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007a00    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007a18    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007a30    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007a48    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007a60    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007a78    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007a90    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007aa8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007ac0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007ad8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007af0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007b08    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007b20    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007b38    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007b50    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00007b68    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00007b80    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007b98    00000018            : vsprintf.c.obj (.text._outs)
                  00007bb0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007bc6    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007bdc    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007bf2    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007c08    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00007c1e    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007c34    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007c4a    00000016     SysTick.o (.text.SysGetTick)
                  00007c60    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007c76    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007c8a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007c9e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007cb2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00007cc6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007cda    00000002     --HOLE-- [fill = 0]
                  00007cdc    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007cf0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007d04    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007d18    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007d2c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007d40    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007d54    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007d68    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007d7c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007d90    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007da4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007db8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007dca    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007ddc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007dee    00000002     --HOLE-- [fill = 0]
                  00007df0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007e00    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007e10    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007e20    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007e30    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007e3e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007e4c    0000000e     MPU6050.o (.text.tap_cb)
                  00007e5a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007e68    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007e74    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007e80    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007e8a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007e94    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007ea4    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007eae    00000002     --HOLE-- [fill = 0]
                  00007eb0    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007ec0    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007eca    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007ed4    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007ede    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007ee8    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007ef8    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00007f02    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007f0c    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007f14    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007f1c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007f24    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007f2a    00000002     --HOLE-- [fill = 0]
                  00007f2c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007f3c    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007f42    00000006            : exit.c.obj (.text:abort)
                  00007f48    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007f4c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007f50    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007f54    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007f58    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007f68    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007f6c    00000004     --HOLE-- [fill = 0]

.cinit     0    00009640    00000088     
                  00009640    00000064     (.cinit..data.load) [load image, compression = lzss]
                  000096a4    0000000c     (__TI_handler_table)
                  000096b0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000096b8    00000010     (__TI_cinit_table)

.rodata    0    00007f70    000016d0     
                  00007f70    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008b66    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009156    00000228     OLED_Font.o (.rodata.asc2_0806)
                  0000937e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009380    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009481    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009488    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000094c8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000094f0    00000028     inv_mpu.o (.rodata.test)
                  00009518    0000001e     inv_mpu.o (.rodata.reg)
                  00009536    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009538    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009550    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009568    00000014     Task_App.o (.rodata.str1.11952760121962574671.1)
                  0000957c    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009590    00000014     Task_App.o (.rodata.str1.492715258893803702.1)
                  000095a4    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000095b5    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000095c6    00000011     Task_App.o (.rodata.str1.3850258909703972507.1)
                  000095d7    00000011     Task_App.o (.rodata.str1.5883415095785080416.1)
                  000095e8    0000000c     inv_mpu.o (.rodata.hw)
                  000095f4    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000095fe    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009600    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00009608    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00009610    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009618    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000961e    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009623    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009627    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000962b    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  0000962e    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00009631    0000000f     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    0000018e     UNINITIALIZED
                  20200324    00000040     Motor.o (.data.Motor_Back_Left)
                  20200364    00000040     Motor.o (.data.Motor_Back_Right)
                  202003a4    00000040     Motor.o (.data.Motor_Font_Left)
                  202003e4    00000040     Motor.o (.data.Motor_Font_Right)
                  20200424    0000002c     inv_mpu.o (.data.st)
                  20200450    00000010     Task_App.o (.data.Motor)
                  20200460    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200470    0000000e     MPU6050.o (.data.hal)
                  2020047e    00000009     MPU6050.o (.data.gyro_orientation)
                  20200487    00000001     Task_App.o (.data.Flag_LED)
                  20200488    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200490    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200498    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  2020049c    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004a0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004a4    00000004     SysTick.o (.data.delayTick)
                  202004a8    00000004     SysTick.o (.data.uwTick)
                  202004ac    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ae    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004af    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202004b0    00000001     Task.o (.data.Task_Num)
                  202004b1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3350    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3390    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     1140    128       44     
       Interrupt.o                    622     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1762    128       50     
                                                               
    .\BSP\Src\
       MPU6050.o                      2460    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1846    0         0      
       Motor.o                        692     0         256    
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         7040    2072      1087   
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     292     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1048    0         0      
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8356    355       4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2984    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       136       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32390   6153      1713   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000096b8 records: 2, size/record: 8, table size: 16
	.data: load addr=00009640, load size=00000064 bytes, run addr=20200324, run size=0000018e bytes, compression=lzss
	.bss: load addr=000096b0, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000096a4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000025b9     00007e94     00007e92   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000404d     00007eb0     00007eac   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007ec8          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007edc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007f12          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007f40          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003b49     00007ee8     00007ee6   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000025c3     00007f2c     00007f28   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007f52          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000072f9     00007f58     00007f54   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007f49  ADC0_IRQHandler                      
00007f49  ADC1_IRQHandler                      
00007f49  AES_IRQHandler                       
00007f4c  C$$EXIT                              
00007f49  CANFD0_IRQHandler                    
00007f49  DAC0_IRQHandler                      
00007e81  DL_Common_delayCycles                
00006575  DL_DMA_initChannel                   
00006009  DL_I2C_fillControllerTXFIFO          
00006bcd  DL_I2C_flushControllerTXFIFO         
00007393  DL_I2C_setClockConfig                
00004215  DL_SYSCTL_configSYSPLL               
00005c35  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000677d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004721  DL_Timer_initPWMMode                 
000076a9  DL_Timer_setCaptCompUpdateMethod     
00007a79  DL_Timer_setCaptureCompareOutCtl     
00007e01  DL_Timer_setCaptureCompareValue      
000076c5  DL_Timer_setClockConfig              
000066a5  DL_UART_init                         
00007db9  DL_UART_setClockConfig               
00007f49  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200488  Data_MotorEncoder                    
20200498  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200490  Data_Tracker_Input                   
2020049c  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00007f49  Default_Handler                      
00007465  Delay                                
20200318  ExISR_Flag                           
20200487  Flag_LED                             
202004ae  Flag_MPU6050_Ready                   
00007f49  GROUP0_IRQHandler                    
00002bbd  GROUP1_IRQHandler                    
00007f4d  HOSTexit                             
00007f49  HardFault_Handler                    
00007f49  I2C0_IRQHandler                      
00007f49  I2C1_IRQHandler                      
00005a97  I2C_OLED_Clear                       
00006c09  I2C_OLED_Set_Pos                     
00005179  I2C_OLED_WR_Byte                     
00005e29  I2C_OLED_i2c_sda_unlock              
00006335  Interrupt_Init                       
00005e89  Key_Read                             
00002d25  MPU6050_Init                         
20200450  Motor                                
20200324  Motor_Back_Left                      
20200364  Motor_Back_Right                     
202003a4  Motor_Font_Left                      
202003e4  Motor_Font_Right                     
00004e59  Motor_GetSpeed                       
00005041  Motor_SetDuty                        
0000496d  Motor_Start                          
000059b9  MyPrintf_DMA                         
00007f49  NMI_Handler                          
00003a39  OLED_Init                            
0000660d  OLED_Printf                          
00003349  OLED_ShowChar                        
00005a29  OLED_ShowString                      
00007115  PID_IQ_Init                          
000036d1  PID_IQ_Prosc                         
000067c1  PID_IQ_SetParams                     
00007f49  PendSV_Handler                       
00007f49  RTC_IRQHandler                       
00001821  Read_Quad                            
00007f55  Reset_Handler                        
00007f49  SPI0_IRQHandler                      
00007f49  SPI1_IRQHandler                      
00007f49  SVC_Handler                          
00006f45  SYSCFG_DL_DMA_CH_RX_init             
00007b39  SYSCFG_DL_DMA_CH_TX_init             
00007e69  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
0000617d  SYSCFG_DL_I2C_MPU6050_init           
00005c99  SYSCFG_DL_I2C_OLED_init              
000055e5  SYSCFG_DL_MotorBack_init             
00005665  SYSCFG_DL_MotorFront_init            
00006069  SYSCFG_DL_SYSCTL_init                
00007e11  SYSCFG_DL_SYSTICK_init               
00005459  SYSCFG_DL_UART0_init                 
00007065  SYSCFG_DL_init                       
00004fa1  SYSCFG_DL_initPower                  
000061d5  Serial_Init                          
20200000  Serial_RxData                        
00007c4b  SysGetTick                           
000058c9  SysTick_Handler                      
000072a9  SysTick_Increasment                  
00007e75  Sys_GetTick                          
00007f49  TIMA0_IRQHandler                     
00007f49  TIMA1_IRQHandler                     
00007f49  TIMG0_IRQHandler                     
00007f49  TIMG12_IRQHandler                    
00007f49  TIMG6_IRQHandler                     
00007f49  TIMG7_IRQHandler                     
00007f49  TIMG8_IRQHandler                     
00007dcb  TI_memcpy_small                      
00007e5b  TI_memset_small                      
00004b9d  Task_Add                             
00005ee9  Task_IdleFunction                    
00004c51  Task_Init                            
00006805  Task_Key                             
00006d71  Task_LED                             
00003d5d  Task_Motor_PID                       
000042f1  Task_OLED                            
000056e5  Task_Serial                          
00002269  Task_Start                           
000060c5  Task_Tracker                         
00002e69  Tracker_Read                         
00007f49  UART0_IRQHandler                     
00007f49  UART1_IRQHandler                     
00007f49  UART2_IRQHandler                     
00007f49  UART3_IRQHandler                     
00007b51  _IQ24div                             
00007b69  _IQ24mpy                             
00006f75  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000096b8  __TI_CINIT_Base                      
000096c8  __TI_CINIT_Limit                     
000096c8  __TI_CINIT_Warm                      
000096a4  __TI_Handler_Table_Base              
000096b0  __TI_Handler_Table_Limit             
00006cbd  __TI_auto_init_nobinit_nopinit       
00005765  __TI_decompress_lzss                 
00007ddd  __TI_decompress_none                 
0000622d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007c61  __TI_zero_init_nomemset              
000025c3  __adddf3                             
000044af  __addsf3                             
00009380  __aeabi_ctype_table_                 
00009380  __aeabi_ctype_table_C                
000058d1  __aeabi_d2f                          
00006659  __aeabi_d2iz                         
00006959  __aeabi_d2uiz                        
000025c3  __aeabi_dadd                         
00005d61  __aeabi_dcmpeq                       
00005d9d  __aeabi_dcmpge                       
00005db1  __aeabi_dcmpgt                       
00005d89  __aeabi_dcmple                       
00005d75  __aeabi_dcmplt                       
00003b49  __aeabi_ddiv                         
0000404d  __aeabi_dmul                         
000025b9  __aeabi_dsub                         
202004a0  __aeabi_errno                        
00007f15  __aeabi_errno_addr                   
000069dd  __aeabi_f2d                          
00006da9  __aeabi_f2iz                         
000044af  __aeabi_fadd                         
00005dc5  __aeabi_fcmpeq                       
00005e01  __aeabi_fcmpge                       
00005e15  __aeabi_fcmpgt                       
00005ded  __aeabi_fcmple                       
00005dd9  __aeabi_fcmplt                       
00005561  __aeabi_fdiv                         
00005341  __aeabi_fmul                         
000044a5  __aeabi_fsub                         
000070bd  __aeabi_i2d                          
00006c45  __aeabi_i2f                          
000062dd  __aeabi_idiv                         
0000274b  __aeabi_idiv0                        
000062dd  __aeabi_idivmod                      
00004f9f  __aeabi_ldiv0                        
000074e5  __aeabi_llsl                         
000073dd  __aeabi_lmul                         
00007f1d  __aeabi_memcpy                       
00007f1d  __aeabi_memcpy4                      
00007f1d  __aeabi_memcpy8                      
00007e31  __aeabi_memset                       
00007e31  __aeabi_memset4                      
00007e31  __aeabi_memset8                      
000072d1  __aeabi_ui2f                         
0000699d  __aeabi_uidiv                        
0000699d  __aeabi_uidivmod                     
00007d69  __aeabi_uldivmod                     
000074e5  __ashldi3                            
ffffffff  __binit__                            
00005b01  __cmpdf2                             
00006cf9  __cmpsf2                             
00003b49  __divdf3                             
00005561  __divsf3                             
00005b01  __eqdf2                              
00006cf9  __eqsf2                              
000069dd  __extendsfdf2                        
00006659  __fixdfsi                            
00006da9  __fixsfsi                            
00006959  __fixunsdfsi                         
000070bd  __floatsidf                          
00006c45  __floatsisf                          
000072d1  __floatunsisf                        
00005855  __gedf2                              
00006c81  __gesf2                              
00005855  __gtdf2                              
00006c81  __gtsf2                              
00005b01  __ledf2                              
00006cf9  __lesf2                              
00005b01  __ltdf2                              
00006cf9  __ltsf2                              
UNDEFED   __mpu_init                           
0000404d  __muldf3                             
000073dd  __muldi3                             
00006d35  __muldsi3                            
00005341  __mulsf3                             
00005b01  __nedf2                              
00006cf9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000025b9  __subdf3                             
000044a5  __subsf3                             
000058d1  __truncdfsf2                         
00004efd  __udivmoddi4                         
000072f9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007f69  _system_pre_init                     
00007f43  abort                                
00009156  asc2_0806                            
00008b66  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000274d  atan2                                
0000274d  atan2l                               
00000df5  atanl                                
00006a1d  atoi                                 
ffffffff  binit                                
202004a4  delayTick                            
000066ed  dmp_enable_6x_lp_quat                
00001371  dmp_enable_feature                   
00005f49  dmp_enable_gyro_cal                  
00006735  dmp_enable_lp_quat                   
000076fd  dmp_load_motion_driver_firmware      
00001e99  dmp_read_fifo                        
00007d7d  dmp_register_android_orient_cb       
00007d91  dmp_register_tap_cb                  
00005211  dmp_set_fifo_rate                    
000028d5  dmp_set_orientation                  
00006849  dmp_set_shake_reject_thresh          
00006eb1  dmp_set_shake_reject_time            
00006ee3  dmp_set_shake_reject_timeout         
00005bcf  dmp_set_tap_axes                     
0000688d  dmp_set_tap_count                    
000015e9  dmp_set_tap_thresh                   
00007005  dmp_set_tap_time                     
00007035  dmp_set_tap_time_multi               
202004b1  enable_group1_irq                    
00006121  frexp                                
00006121  frexpl                               
000095e8  hw                                   
00000000  interruptVectors                     
000043cd  ldexp                                
000043cd  ldexpl                               
00007485  main                                 
00007401  memccpy                              
000074a5  memcmp                               
20200322  more                                 
00005cfd  mpu6050_i2c_sda_unlock               
00004a29  mpu_configure_fifo                   
00005945  mpu_get_accel_fsr                    
00005fa9  mpu_get_gyro_fsr                     
00006e7d  mpu_get_sample_rate                  
000035a9  mpu_init                             
000037f5  mpu_load_firmware                    
00003e61  mpu_lp_accel_mode                    
00003c55  mpu_read_fifo_stream                 
00004d01  mpu_read_mem                         
00001a4d  mpu_reset_fifo                       
00004131  mpu_set_accel_fsr                    
00002419  mpu_set_bypass                       
00004ae5  mpu_set_dmp_state                    
000047e5  mpu_set_gyro_fsr                     
000050dd  mpu_set_int_latched                  
00004651  mpu_set_lpf                          
00003f61  mpu_set_sample_rate                  
00003479  mpu_set_sensors                      
00004dad  mpu_write_mem                        
000030e1  mspm0_i2c_read                       
000048a9  mspm0_i2c_write                      
00003215  qsort                                
202002f0  quat                                 
00009518  reg                                  
000043cd  scalbn                               
000043cd  scalbnl                              
2020031c  sensor_timestamp                     
20200320  sensors                              
00002a4d  sqrt                                 
00002a4d  sqrtl                                
000094f0  test                                 
202004a8  uwTick                               
00006a5d  vsnprintf                            
000070e9  vsprintf                             
00007e21  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
00001371  dmp_enable_feature                   
000015e9  dmp_set_tap_thresh                   
00001821  Read_Quad                            
00001a4d  mpu_reset_fifo                       
00001e99  dmp_read_fifo                        
00002269  Task_Start                           
00002419  mpu_set_bypass                       
000025b9  __aeabi_dsub                         
000025b9  __subdf3                             
000025c3  __adddf3                             
000025c3  __aeabi_dadd                         
0000274b  __aeabi_idiv0                        
0000274d  atan2                                
0000274d  atan2l                               
000028d5  dmp_set_orientation                  
00002a4d  sqrt                                 
00002a4d  sqrtl                                
00002bbd  GROUP1_IRQHandler                    
00002d25  MPU6050_Init                         
00002e69  Tracker_Read                         
000030e1  mspm0_i2c_read                       
00003215  qsort                                
00003349  OLED_ShowChar                        
00003479  mpu_set_sensors                      
000035a9  mpu_init                             
000036d1  PID_IQ_Prosc                         
000037f5  mpu_load_firmware                    
00003a39  OLED_Init                            
00003b49  __aeabi_ddiv                         
00003b49  __divdf3                             
00003c55  mpu_read_fifo_stream                 
00003d5d  Task_Motor_PID                       
00003e61  mpu_lp_accel_mode                    
00003f61  mpu_set_sample_rate                  
0000404d  __aeabi_dmul                         
0000404d  __muldf3                             
00004131  mpu_set_accel_fsr                    
00004215  DL_SYSCTL_configSYSPLL               
000042f1  Task_OLED                            
000043cd  ldexp                                
000043cd  ldexpl                               
000043cd  scalbn                               
000043cd  scalbnl                              
000044a5  __aeabi_fsub                         
000044a5  __subsf3                             
000044af  __addsf3                             
000044af  __aeabi_fadd                         
00004651  mpu_set_lpf                          
00004721  DL_Timer_initPWMMode                 
000047e5  mpu_set_gyro_fsr                     
000048a9  mspm0_i2c_write                      
0000496d  Motor_Start                          
00004a29  mpu_configure_fifo                   
00004ae5  mpu_set_dmp_state                    
00004b9d  Task_Add                             
00004c51  Task_Init                            
00004d01  mpu_read_mem                         
00004dad  mpu_write_mem                        
00004e59  Motor_GetSpeed                       
00004efd  __udivmoddi4                         
00004f9f  __aeabi_ldiv0                        
00004fa1  SYSCFG_DL_initPower                  
00005041  Motor_SetDuty                        
000050dd  mpu_set_int_latched                  
00005179  I2C_OLED_WR_Byte                     
00005211  dmp_set_fifo_rate                    
00005341  __aeabi_fmul                         
00005341  __mulsf3                             
00005459  SYSCFG_DL_UART0_init                 
00005561  __aeabi_fdiv                         
00005561  __divsf3                             
000055e5  SYSCFG_DL_MotorBack_init             
00005665  SYSCFG_DL_MotorFront_init            
000056e5  Task_Serial                          
00005765  __TI_decompress_lzss                 
00005855  __gedf2                              
00005855  __gtdf2                              
000058c9  SysTick_Handler                      
000058d1  __aeabi_d2f                          
000058d1  __truncdfsf2                         
00005945  mpu_get_accel_fsr                    
000059b9  MyPrintf_DMA                         
00005a29  OLED_ShowString                      
00005a97  I2C_OLED_Clear                       
00005b01  __cmpdf2                             
00005b01  __eqdf2                              
00005b01  __ledf2                              
00005b01  __ltdf2                              
00005b01  __nedf2                              
00005bcf  dmp_set_tap_axes                     
00005c35  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005c99  SYSCFG_DL_I2C_OLED_init              
00005cfd  mpu6050_i2c_sda_unlock               
00005d61  __aeabi_dcmpeq                       
00005d75  __aeabi_dcmplt                       
00005d89  __aeabi_dcmple                       
00005d9d  __aeabi_dcmpge                       
00005db1  __aeabi_dcmpgt                       
00005dc5  __aeabi_fcmpeq                       
00005dd9  __aeabi_fcmplt                       
00005ded  __aeabi_fcmple                       
00005e01  __aeabi_fcmpge                       
00005e15  __aeabi_fcmpgt                       
00005e29  I2C_OLED_i2c_sda_unlock              
00005e89  Key_Read                             
00005ee9  Task_IdleFunction                    
00005f49  dmp_enable_gyro_cal                  
00005fa9  mpu_get_gyro_fsr                     
00006009  DL_I2C_fillControllerTXFIFO          
00006069  SYSCFG_DL_SYSCTL_init                
000060c5  Task_Tracker                         
00006121  frexp                                
00006121  frexpl                               
0000617d  SYSCFG_DL_I2C_MPU6050_init           
000061d5  Serial_Init                          
0000622d  __TI_ltoa                            
000062dd  __aeabi_idiv                         
000062dd  __aeabi_idivmod                      
00006335  Interrupt_Init                       
00006575  DL_DMA_initChannel                   
0000660d  OLED_Printf                          
00006659  __aeabi_d2iz                         
00006659  __fixdfsi                            
000066a5  DL_UART_init                         
000066ed  dmp_enable_6x_lp_quat                
00006735  dmp_enable_lp_quat                   
0000677d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000067c1  PID_IQ_SetParams                     
00006805  Task_Key                             
00006849  dmp_set_shake_reject_thresh          
0000688d  dmp_set_tap_count                    
00006959  __aeabi_d2uiz                        
00006959  __fixunsdfsi                         
0000699d  __aeabi_uidiv                        
0000699d  __aeabi_uidivmod                     
000069dd  __aeabi_f2d                          
000069dd  __extendsfdf2                        
00006a1d  atoi                                 
00006a5d  vsnprintf                            
00006bcd  DL_I2C_flushControllerTXFIFO         
00006c09  I2C_OLED_Set_Pos                     
00006c45  __aeabi_i2f                          
00006c45  __floatsisf                          
00006c81  __gesf2                              
00006c81  __gtsf2                              
00006cbd  __TI_auto_init_nobinit_nopinit       
00006cf9  __cmpsf2                             
00006cf9  __eqsf2                              
00006cf9  __lesf2                              
00006cf9  __ltsf2                              
00006cf9  __nesf2                              
00006d35  __muldsi3                            
00006d71  Task_LED                             
00006da9  __aeabi_f2iz                         
00006da9  __fixsfsi                            
00006e7d  mpu_get_sample_rate                  
00006eb1  dmp_set_shake_reject_time            
00006ee3  dmp_set_shake_reject_timeout         
00006f45  SYSCFG_DL_DMA_CH_RX_init             
00006f75  _IQ24toF                             
00007005  dmp_set_tap_time                     
00007035  dmp_set_tap_time_multi               
00007065  SYSCFG_DL_init                       
000070bd  __aeabi_i2d                          
000070bd  __floatsidf                          
000070e9  vsprintf                             
00007115  PID_IQ_Init                          
000072a9  SysTick_Increasment                  
000072d1  __aeabi_ui2f                         
000072d1  __floatunsisf                        
000072f9  _c_int00_noargs                      
00007393  DL_I2C_setClockConfig                
000073dd  __aeabi_lmul                         
000073dd  __muldi3                             
00007401  memccpy                              
00007465  Delay                                
00007485  main                                 
000074a5  memcmp                               
000074e5  __aeabi_llsl                         
000074e5  __ashldi3                            
000076a9  DL_Timer_setCaptCompUpdateMethod     
000076c5  DL_Timer_setClockConfig              
000076fd  dmp_load_motion_driver_firmware      
00007a79  DL_Timer_setCaptureCompareOutCtl     
00007b39  SYSCFG_DL_DMA_CH_TX_init             
00007b51  _IQ24div                             
00007b69  _IQ24mpy                             
00007c4b  SysGetTick                           
00007c61  __TI_zero_init_nomemset              
00007d69  __aeabi_uldivmod                     
00007d7d  dmp_register_android_orient_cb       
00007d91  dmp_register_tap_cb                  
00007db9  DL_UART_setClockConfig               
00007dcb  TI_memcpy_small                      
00007ddd  __TI_decompress_none                 
00007e01  DL_Timer_setCaptureCompareValue      
00007e11  SYSCFG_DL_SYSTICK_init               
00007e21  wcslen                               
00007e31  __aeabi_memset                       
00007e31  __aeabi_memset4                      
00007e31  __aeabi_memset8                      
00007e5b  TI_memset_small                      
00007e69  SYSCFG_DL_DMA_init                   
00007e75  Sys_GetTick                          
00007e81  DL_Common_delayCycles                
00007f15  __aeabi_errno_addr                   
00007f1d  __aeabi_memcpy                       
00007f1d  __aeabi_memcpy4                      
00007f1d  __aeabi_memcpy8                      
00007f43  abort                                
00007f49  ADC0_IRQHandler                      
00007f49  ADC1_IRQHandler                      
00007f49  AES_IRQHandler                       
00007f49  CANFD0_IRQHandler                    
00007f49  DAC0_IRQHandler                      
00007f49  DMA_IRQHandler                       
00007f49  Default_Handler                      
00007f49  GROUP0_IRQHandler                    
00007f49  HardFault_Handler                    
00007f49  I2C0_IRQHandler                      
00007f49  I2C1_IRQHandler                      
00007f49  NMI_Handler                          
00007f49  PendSV_Handler                       
00007f49  RTC_IRQHandler                       
00007f49  SPI0_IRQHandler                      
00007f49  SPI1_IRQHandler                      
00007f49  SVC_Handler                          
00007f49  TIMA0_IRQHandler                     
00007f49  TIMA1_IRQHandler                     
00007f49  TIMG0_IRQHandler                     
00007f49  TIMG12_IRQHandler                    
00007f49  TIMG6_IRQHandler                     
00007f49  TIMG7_IRQHandler                     
00007f49  TIMG8_IRQHandler                     
00007f49  UART0_IRQHandler                     
00007f49  UART1_IRQHandler                     
00007f49  UART2_IRQHandler                     
00007f49  UART3_IRQHandler                     
00007f4c  C$$EXIT                              
00007f4d  HOSTexit                             
00007f55  Reset_Handler                        
00007f69  _system_pre_init                     
00008b66  asc2_1608                            
00009156  asc2_0806                            
00009380  __aeabi_ctype_table_                 
00009380  __aeabi_ctype_table_C                
000094f0  test                                 
00009518  reg                                  
000095e8  hw                                   
000096a4  __TI_Handler_Table_Base              
000096b0  __TI_Handler_Table_Limit             
000096b8  __TI_CINIT_Base                      
000096c8  __TI_CINIT_Limit                     
000096c8  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200364  Motor_Back_Right                     
202003a4  Motor_Font_Left                      
202003e4  Motor_Font_Right                     
20200450  Motor                                
20200487  Flag_LED                             
20200488  Data_MotorEncoder                    
20200490  Data_Tracker_Input                   
20200498  Data_Motor_TarSpeed                  
2020049c  Data_Tracker_Offset                  
202004a0  __aeabi_errno                        
202004a4  delayTick                            
202004a8  uwTick                               
202004ae  Flag_MPU6050_Ready                   
202004b1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[311 symbols]
